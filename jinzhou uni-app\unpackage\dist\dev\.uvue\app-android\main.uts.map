{"version": 3, "file": "main.uts", "sourceRoot": "", "sources": ["main.uts"], "names": [], "mappings": "AAAA,OAAO,wGAAwG,CAAC;AAAA,OAAO,GAAG,MAAM,YAAY,CAAA;AAE5I,OAAO,EAAE,YAAY,EAAE,MAAM,KAAK,CAAA;AAClC,MAAM,UAAU,SAAS;IACxB,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAA;IAC7B,OAAO;QACN,GAAG;KACH,CAAA;AACF,CAAC;AACD,MAAM,UAAU,IAAI,CAAC,GAAG,EAAE,IAAI;IAC1B,gBAAgB,EAAE,CAAC;IACnB,eAAe,EAAE,CAAC;IAClB,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,EAAE,CAAC,CAAC;AAC3D,CAAC;AAED,MAAM,OAAO,YAAa,SAAQ,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS;IACjE,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ,CAAA;IAChC,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,gBAAgB,CAAA;IACzC,QAAQ,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAA;IACtC,QAAQ,CAAC,WAAW,EAAE,MAAM,GAAG,KAAK,CAAA;IACpC,QAAQ,CAAC,kBAAkB,EAAE,MAAM,GAAG,MAAM,CAAA;IAE5C,gBAAgB,KAAK,EAAE,CAAA,CAAC,CAAC;CAC5B;AAED,OAAO,uBAAuB,MAAM,oCAAoC,CAAA;AACxE,OAAO,2BAA2B,MAAM,wCAAwC,CAAA;AAChF,SAAS,gBAAgB;IACzB,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,SAAS,EAAE,uBAAuB,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,WAAW,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,wBAAwB,EAAC,QAAQ,CAAC,EAAC,CAAC,iBAAiB,EAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,YAAY,CAAC,CAAA;IAC/N,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,uBAAuB,EAAE,SAAS,EAAE,2BAA2B,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,WAAW,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,wBAAwB,EAAC,QAAQ,CAAC,EAAC,CAAC,8BAA8B,EAAC,SAAS,CAAC,EAAC,CAAC,wBAAwB,EAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,YAAY,CAAC,CAAA;AACzR,CAAC;AACD,MAAM,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,CAAA;AACxD,MAAM,eAAe,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,EAAC,mBAAmB,CAAC,EAAC,CAAC,OAAO,EAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB,EAAC,QAAQ,CAAC,EAAC,CAAC,iBAAiB,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAC/K,SAAS,eAAe;IACtB,WAAW,CAAC,aAAa,GAAG,oBAAoB,CAAA;IAChD,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,wBAAwB,EAAC,OAAO,CAAC,EAAC,CAAC,wBAAwB,EAAC,QAAQ,CAAC,EAAC,CAAC,8BAA8B,EAAC,SAAS,CAAC,EAAC,CAAC,iBAAiB,EAAC,SAAS,CAAC,CAAC,CAAC,CAAA;IACrL,WAAW,CAAC,eAAe,GAAG,IAAG,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAE,IAAI,CAAA;IACjE,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,eAAe,EAAE,CAAA;IAClD,WAAW,CAAC,YAAY,GAAG,EAAE,CAAA;IAC7B,WAAW,CAAC,WAAW,GAAG,QAAQ,EAAE,CAAA;IAEpC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAA;AAC1B,CAAC", "sourcesContent": ["import 'D:/新建文件夹/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/index.ts';import App from './App.uvue'\r\n\r\nimport { createSSRApp } from 'vue'\r\nexport function createApp() {\r\n\tconst app = createSSRApp(App)\r\n\treturn {\r\n\t\tapp\r\n\t}\r\n}\nexport function main(app: IApp) {\n    definePageRoutes();\n    defineAppConfig();\n    (createApp()['app'] as VueApp).mount(app, GenUniApp());\n}\n\nexport class UniAppConfig extends io.dcloud.uniapp.appframe.AppConfig {\n    override name: string = \"金舟国际物流\"\n    override appid: string = \"__UNI__0CFFF19\"\n    override versionName: string = \"1.0.0\"\n    override versionCode: string = \"100\"\n    override uniCompilerVersion: string = \"4.66\"\n    \n    constructor() { super() }\n}\n\nimport GenPagesIndexIndexClass from './pages/index/index.uvue?type=page'\nimport GenPagesWebviewWebviewClass from './pages/webview/webview.uvue?type=page'\nfunction definePageRoutes() {\n__uniRoutes.push({ path: \"pages/index/index\", component: GenPagesIndexIndexClass, meta: { isQuit: true } as UniPageMeta, style: utsMapOf([[\"navigationBarTitleText\",\"金舟国际物流\"],[\"navigationStyle\",\"custom\"]]) } as UniPageRoute)\n__uniRoutes.push({ path: \"pages/webview/webview\", component: GenPagesWebviewWebviewClass, meta: { isQuit: false } as UniPageMeta, style: utsMapOf([[\"navigationBarTitleText\",\"金舟国际物流\"],[\"navigationBarBackgroundColor\",\"#0c4da2\"],[\"navigationBarTextStyle\",\"white\"]]) } as UniPageRoute)\n}\nconst __uniTabBar: Map<string, any | null> | null = null\nconst __uniLaunchPage: Map<string, any | null> = utsMapOf([[\"url\",\"pages/index/index\"],[\"style\",utsMapOf([[\"navigationBarTitleText\",\"金舟国际物流\"],[\"navigationStyle\",\"custom\"]])]])\nfunction defineAppConfig(){\n  __uniConfig.entryPagePath = '/pages/index/index'\n  __uniConfig.globalStyle = utsMapOf([[\"navigationBarTextStyle\",\"white\"],[\"navigationBarTitleText\",\"金舟国际物流\"],[\"navigationBarBackgroundColor\",\"#0c4da2\"],[\"backgroundColor\",\"#f8f9fa\"]])\n  __uniConfig.getTabBarConfig = ():Map<string, any> | null =>  null\n  __uniConfig.tabBar = __uniConfig.getTabBarConfig()\n  __uniConfig.conditionUrl = ''\n  __uniConfig.uniIdRouter = utsMapOf()\n  \n  __uniConfig.ready = true\n}\n"]}