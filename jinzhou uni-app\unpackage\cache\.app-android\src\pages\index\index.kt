@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNI0CFFF19
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import io.dcloud.uniapp.extapi.getSystemInfoSync as uni_getSystemInfoSync
import io.dcloud.uniapp.extapi.navigateTo as uni_navigateTo
import io.dcloud.uniapp.extapi.showModal as uni_showModal
import io.dcloud.uniapp.extapi.showToast as uni_showToast
open class GenPagesIndexIndex : BasePage {
    constructor(__ins: ComponentInternalInstance, __renderer: String?) : super(__ins, __renderer) {
        onLoad(fun(_: OnLoadOptions) {
            val systemInfo = uni_getSystemInfoSync()
            this.statusBarHeight = systemInfo.statusBarHeight || 0
        }
        , __ins)
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        return createElementVNode("view", utsMapOf("class" to "container"), utsArrayOf(
            createElementVNode("view", utsMapOf("class" to "status-bar", "style" to normalizeStyle(utsMapOf("height" to (_ctx.statusBarHeight + "px")))), null, 4),
            createElementVNode("view", utsMapOf("class" to "main-content"), utsArrayOf(
                createElementVNode("image", utsMapOf("class" to "logo-img", "src" to "/static/logo.png", "mode" to "aspectFit")),
                createElementVNode("view", utsMapOf("class" to "title-container"), utsArrayOf(
                    createElementVNode("text", utsMapOf("class" to "main-title"), utsArrayOf(
                        createElementVNode("text", utsMapOf("class" to "gold-text"), "金舟"),
                        createElementVNode("text", utsMapOf("class" to "blue-text"), "国际物流")
                    ))
                )),
                createElementVNode("text", utsMapOf("class" to "sub-title"), "Jin Zhou International Logistics"),
                createElementVNode("view", utsMapOf("class" to "button-container", "onClick" to _ctx.enterApp), utsArrayOf(
                    createElementVNode("text", utsMapOf("class" to "btn-text"), "进入官网"),
                    createElementVNode("text", utsMapOf("class" to "btn-subtext"), "Enter Website")
                ), 8, utsArrayOf(
                    "onClick"
                ))
            ))
        ))
    }
    open var statusBarHeight: Number by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return utsMapOf("statusBarHeight" to 0)
    }
    open var enterApp = ::gen_enterApp_fn
    open fun gen_enterApp_fn() {
        console.log("点击进入官网按钮", " at pages/index/index.uvue:44")
        try {
            uni_navigateTo(NavigateToOptions(url = "/pages/webview/webview", success = fun(res){
                console.log("导航成功:", res, " at pages/index/index.uvue:51")
            }
            , fail = fun(err){
                console.error("导航失败:", err, " at pages/index/index.uvue:54")
                uni_showModal(ShowModalOptions(title = "提示", content = "无法打开内置浏览器，是否在外部浏览器中打开？", success = fun(modalRes){
                    if (modalRes.confirm) {
                        uni_showToast(ShowToastOptions(title = "请手动访问 www.jinzhouil.com", duration = 3000, icon = "none"))
                    }
                }
                ))
            }
            ))
        }
         catch (error: Throwable) {
            console.error("跳转异常:", error, " at pages/index/index.uvue:78")
            uni_showToast(ShowToastOptions(title = "页面跳转失败", icon = "none"))
        }
    }
    companion object {
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            normalizeCssStyles(utsArrayOf(
                styles0
            ), utsArrayOf(
                GenApp.styles
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return utsMapOf("container" to padStyleMapOf(utsMapOf("width" to "100%", "backgroundColor" to "#f8f9fa", "display" to "flex", "flexDirection" to "column")), "status-bar" to padStyleMapOf(utsMapOf("width" to "100%", "backgroundImage" to "none", "backgroundColor" to "rgba(0,0,0,0)")), "main-content" to padStyleMapOf(utsMapOf("flex" to 1, "display" to "flex", "flexDirection" to "column", "justifyContent" to "center", "alignItems" to "center", "textAlign" to "center", "paddingTop" to 20, "paddingRight" to 20, "paddingBottom" to 20, "paddingLeft" to 20)), "logo-img" to padStyleMapOf(utsMapOf("width" to 120, "height" to 120, "marginBottom" to 20)), "title-container" to padStyleMapOf(utsMapOf("marginBottom" to 10)), "main-title" to padStyleMapOf(utsMapOf("fontSize" to 48, "fontWeight" to "normal", "lineHeight" to 1.2)), "gold-text" to padStyleMapOf(utsMapOf("color" to "#D4AF37")), "blue-text" to padStyleMapOf(utsMapOf("color" to "#0c4da2")), "sub-title" to padStyleMapOf(utsMapOf("color" to "#666666", "fontSize" to 24, "marginBottom" to 30)), "button-container" to padStyleMapOf(utsMapOf("paddingTop" to 12, "paddingRight" to 30, "paddingBottom" to 12, "paddingLeft" to 30, "backgroundColor" to "#0c4da2", "color" to "#ffffff", "borderTopLeftRadius" to 5, "borderTopRightRadius" to 5, "borderBottomRightRadius" to 5, "borderBottomLeftRadius" to 5, "cursor" to "pointer", "transitionProperty" to "backgroundColor", "transitionDuration" to "0.3s", "backgroundColor:active" to "#083778")), "btn-text" to padStyleMapOf(utsMapOf("fontSize" to 18, "color" to "#ffffff", "marginBottom" to 5)), "btn-subtext" to padStyleMapOf(utsMapOf("fontSize" to 14, "color" to "#ffffff")), "@TRANSITION" to utsMapOf("button-container" to utsMapOf("property" to "backgroundColor", "duration" to "0.3s")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = utsMapOf()
        var emits: Map<String, Any?> = utsMapOf()
        var props = normalizePropsOptions(utsMapOf())
        var propsNeedCastKeys: UTSArray<String> = utsArrayOf()
        var components: Map<String, CreateVueComponent> = utsMapOf()
    }
}
