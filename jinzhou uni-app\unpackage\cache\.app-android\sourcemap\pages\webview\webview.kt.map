{"version": 3, "sources": ["pages/webview/webview.uvue", "App.uvue", "pages/webview/webview.uvue?type=page"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<web-view :src=\"webUrl\" @message=\"handleMessage\" @error=\"handleError\"></web-view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\twebUrl: ''\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\ttry {\n\t\t\t\t// 获取传入的URL参数\n\t\t\t\tif (options && options.url) {\n\t\t\t\t\tthis.webUrl = decodeURIComponent(options.url)\n\t\t\t\t} else {\n\t\t\t\t\t// 默认URL - 指向线上服务器的主页\n\t\t\t\t\tthis.webUrl = 'http://www.jinzhouil.com/main.html'\n\t\t\t\t}\n\n\t\t\t\tconsole.log('加载网页:', this.webUrl)\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('页面加载错误:', error)\n\t\t\t\tthis.webUrl = 'http://www.jinzhouil.com/main.html'\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\thandleMessage(event) {\n\t\t\t\tconsole.log('收到网页消息:', event.detail.data)\n\t\t\t},\n\t\t\thandleError(event) {\n\t\t\t\tconsole.error('网页加载错误:', event.detail)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网页加载失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\twidth: 100%;\n\t\theight: 100vh;\n\t}\n</style>\n", null, null], "names": [], "mappings": ";;;;;;;;;;;;;;;;;+BAoBK;AAbC;;eAMJ,IAAO,sBAAO,EAAA;YACb,IAAI;gBAEH,IAAI,WAAW,QAAQ,GAAG,EAAE;oBAC3B,IAAI,CAAC,MAAK,GAAI,WAAW,iBAAiB,CAAC,mBAAC,QAAA,GAAA,GAAA;uBACtC;oBAEN,IAAI,CAAC,MAAK,GAAI;;gBAGf,QAAQ,GAAG,CAAC,SAAS,IAAI,CAAC,MAAM,EAAA;;aAC/B,OAAO,kBAAO;gBACf,QAAQ,KAAK,CAAC,WAAW,OAAK;gBAC9B,IAAI,CAAC,MAAK,GAAI;;QAEhB;;;;;;;eA3BD,mBAEO,QAAA,SAFD,WAAM,cAAW;YACtB,mBAAiF,YAAA,SAAtE,SAAK,KAAA,MAAM,EAAG,eAAS,KAAA,aAAa,EAAG,aAAO,KAAA,WAAW;;;;;;;aAQlE;;;wBAAA,YAAQ;;aAoBT;aAAA,qBAAc,KAAK,EAAA;QAClB,QAAQ,GAAG,CAAC,WAAW,MAAM,MAAM,CAAC,IAAI,EAAA;IACzC;aACA;aAAA,mBAAY,KAAK,EAAA;QAChB,QAAQ,KAAK,CAAC,WAAW,MAAM,MAAM,EAAA;QACrC,+BACC,QAAO,UACP,OAAM;IAER;;;;;;;;;;;;;;;;;;;;AAEF"}