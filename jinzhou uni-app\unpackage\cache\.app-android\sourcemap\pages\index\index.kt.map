{"version": 3, "sources": ["pages/index/index.uvue", "pages/index/index.uvue?type=page", "App.uvue"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 状态栏占位 -->\r\n\t\t<view class=\"status-bar\" :style=\"{height: statusBarHeight + 'px'}\"></view>\r\n\r\n\t\t<!-- 主要内容 - 完全按照电脑版样式 -->\r\n\t\t<view class=\"main-content\">\r\n\t\t\t<!-- Logo图片 -->\r\n\t\t\t<image class=\"logo-img\" src=\"/static/logo.png\" mode=\"aspectFit\"></image>\r\n\r\n\t\t\t<!-- 标题 - 完全按照电脑版 -->\r\n\t\t\t<view class=\"title-container\">\r\n\t\t\t\t<text class=\"main-title\">\r\n\t\t\t\t\t<text class=\"gold-text\">金舟</text><text class=\"blue-text\">国际物流</text>\r\n\t\t\t\t</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 英文副标题 -->\r\n\t\t\t<text class=\"sub-title\">Jin Zhou International Logistics</text>\r\n\r\n\t\t\t<!-- 按钮 - 完全按照电脑版样式 -->\r\n\t\t\t<view class=\"button-container\" @click=\"enterApp\">\r\n\t\t\t\t<text class=\"btn-text\">进入官网</text>\r\n\t\t\t\t<text class=\"btn-subtext\">Enter Website</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tstatusBarHeight: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// 获取状态栏高度\r\n\t\t\tconst systemInfo = uni.getSystemInfoSync()\r\n\t\t\tthis.statusBarHeight = systemInfo.statusBarHeight || 0\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tenterApp() {\r\n\t\t\t\tconsole.log('点击进入官网按钮')\r\n\r\n\t\t\t\t// 先尝试简单的页面跳转\r\n\t\t\t\ttry {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/webview/webview',\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tconsole.log('导航成功:', res)\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\tconsole.error('导航失败:', err)\r\n\t\t\t\t\t\t\t// 如果导航失败，显示提示\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\t\tcontent: '无法打开内置浏览器，是否在外部浏览器中打开？',\r\n\t\t\t\t\t\t\t\tsuccess: (modalRes) => {\r\n\t\t\t\t\t\t\t\t\tif (modalRes.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t// 尝试打开外部浏览器\r\n\r\n\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '请手动访问 www.jinzhouil.com',\r\n\t\t\t\t\t\t\t\t\t\t\tduration: 3000,\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('跳转异常:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '页面跳转失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 完全按照电脑版样式设计 */\r\n\t.container {\r\n\t\twidth: 100%;\r\n\t\theight: 100vh;\r\n\t\tbackground-color: #f8f9fa; /* 电脑版的背景色 */\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.status-bar {\r\n\t\twidth: 100%;\r\n\t\tbackground: transparent;\r\n\t}\r\n\r\n\t.main-content {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\ttext-align: center;\r\n\t\tpadding: 20px;\r\n\t}\r\n\r\n\t/* Logo图片 - 按照电脑版尺寸 */\r\n\t.logo-img {\r\n\t\twidth: 120px;\r\n\t\theight: 120px;\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\r\n\t/* 标题容器 */\r\n\t.title-container {\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t/* 主标题 - 完全按照电脑版样式 */\r\n\t.main-title {\r\n\t\tfont-size: 48px;\r\n\t\tfont-weight: normal;\r\n\t\tline-height: 1.2;\r\n\t}\r\n\r\n\t.gold-text {\r\n\t\tcolor: #D4AF37; /* 电脑版的金色 */\r\n\t}\r\n\r\n\t.blue-text {\r\n\t\tcolor: #0c4da2; /* 电脑版的蓝色 */\r\n\t}\r\n\r\n\t/* 英文副标题 - 按照电脑版样式 */\r\n\t.sub-title {\r\n\t\tcolor: #666;\r\n\t\tfont-size: 24px;\r\n\t\tmargin-bottom: 30px;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t/* 按钮容器 - 完全按照电脑版样式 */\r\n\t.button-container {\r\n\t\tdisplay: inline-block;\r\n\t\tpadding: 12px 30px;\r\n\t\tbackground-color: #0c4da2;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 5px;\r\n\t\tcursor: pointer;\r\n\t\ttransition: background-color 0.3s;\r\n\t}\r\n\r\n\t.button-container:active {\r\n\t\tbackground-color: #083778;\r\n\t}\r\n\r\n\t/* 按钮文字 - 按照电脑版样式 */\r\n\t.btn-text {\r\n\t\tfont-size: 18px;\r\n\t\tcolor: #ffffff;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 5px;\r\n\t}\r\n\r\n\t.btn-subtext {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #ffffff;\r\n\t\tdisplay: block;\r\n\t}\r\n</style>\r\n", null, null], "names": [], "mappings": ";;;;;;;;;;;;;;;;;+BAsC0B;+BASjB;+BAQE;+BAVP;AAfE;;eAMJ,sBAAM;YAEL,IAAM,aAAa;YACnB,IAAI,CAAC,eAAc,GAAI,WAAW,eAAc,IAAK,CAAA;QACtD;;;;;;;eAvCD,mBAyBO,QAAA,SAzBD,WAAM,cAAW;YAEtB,mBAA0E,QAAA,SAApE,WAAM,cAAc,WAAK,eAAE,SAAA,aAAA,KAAA,eAAA,GAAA;YAGjC,mBAmBO,QAAA,SAnBD,WAAM,iBAAc;gBAEzB,mBAAwE,SAAA,SAAjE,WAAM,YAAW,SAAI,oBAAmB,UAAK;gBAGpD,mBAIO,QAAA,SAJD,WAAM,oBAAiB;oBAC5B,mBAEO,QAAA,SAFD,WAAM,eAAY;wBACvB,mBAAiC,QAAA,SAA3B,WAAM,cAAY;wBAAS,mBAAmC,QAAA,SAA7B,WAAM,cAAY;;;gBAK3D,mBAA+D,QAAA,SAAzD,WAAM,cAAY;gBAGxB,mBAGO,QAAA,SAHD,WAAM,oBAAoB,aAAO,KAAA,QAAQ;oBAC9C,mBAAkC,QAAA,SAA5B,WAAM,aAAW;oBACvB,mBAA8C,QAAA,SAAxC,WAAM,gBAAc;;;;;;;aAU1B;;;wBAAA,qBAAiB,CAAA;;aASlB;aAAA,kBAAQ;QACP,QAAQ,GAAG,CAAC,YAAU;QAGtB,IAAI;YACH,iCACC,MAAK,0BACL,UAAS,IAAC,IAAM;gBACf,QAAQ,GAAG,CAAC,SAAS,KAAG;YACzB;cACA,OAAM,IAAC,IAAM;gBACZ,QAAQ,KAAK,CAAC,SAAS,KAAG;gBAE1B,+BACC,QAAO,MACP,UAAS,0BACT,UAAS,IAAC,SAAW;oBACpB,IAAI,SAAS,OAAO,EAAE;wBAMrB,+BACC,QAAO,2BACP,WAAU,IAAI,EACd,OAAM;;gBAIT;;YAEF;;;SAEA,OAAO,kBAAO;YACf,QAAQ,KAAK,CAAC,SAAS,OAAK;YAC5B,+BACC,QAAO,UACP,OAAM;;IAGT;;;;;;;;;;;;;;;;;;;;AAEF"}