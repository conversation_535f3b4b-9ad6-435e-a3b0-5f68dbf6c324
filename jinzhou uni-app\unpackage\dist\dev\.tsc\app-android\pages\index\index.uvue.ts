
	const __sfc__ = defineComponent({
		data() {
			return {
				statusBarHeight: 0
			}
		},
		onLoad() {
			// 获取状态栏高度
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight || 0
		},
		methods: {
			enterApp() {
				console.log('点击进入官网按钮', " at pages/index/index.uvue:44")

				// 先尝试简单的页面跳转
				try {
					uni.navigateTo({
						url: '/pages/webview/webview',
						success: (res) => {
							console.log('导航成功:', res, " at pages/index/index.uvue:51")
						},
						fail: (err) => {
							console.error('导航失败:', err, " at pages/index/index.uvue:54")
							// 如果导航失败，显示提示
							uni.showModal({
								title: '提示',
								content: '无法打开内置浏览器，是否在外部浏览器中打开？',
								success: (modalRes) => {
									if (modalRes.confirm) {
										// 尝试打开外部浏览器




										uni.showToast({
											title: '请手动访问 www.jinzhouil.com',
											duration: 3000,
											icon: 'none'
										})

									}
								}
							})
						}
					})
				} catch (error) {
					console.error('跳转异常:', error, " at pages/index/index.uvue:78")
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					})
				}
			}
		}
	})

export default __sfc__
function GenPagesIndexIndexRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
  return createElementVNode("view", utsMapOf({ class: "container" }), [
    createElementVNode("view", utsMapOf({
      class: "status-bar",
      style: normalizeStyle(utsMapOf({height: _ctx.statusBarHeight + 'px'}))
    }), null, 4 /* STYLE */),
    createElementVNode("view", utsMapOf({ class: "main-content" }), [
      createElementVNode("image", utsMapOf({
        class: "logo-img",
        src: "/static/logo.png",
        mode: "aspectFit"
      })),
      createElementVNode("view", utsMapOf({ class: "title-container" }), [
        createElementVNode("text", utsMapOf({ class: "main-title" }), [
          createElementVNode("text", utsMapOf({ class: "gold-text" }), "金舟"),
          createElementVNode("text", utsMapOf({ class: "blue-text" }), "国际物流")
        ])
      ]),
      createElementVNode("text", utsMapOf({ class: "sub-title" }), "Jin Zhou International Logistics"),
      createElementVNode("view", utsMapOf({
        class: "button-container",
        onClick: _ctx.enterApp
      }), [
        createElementVNode("text", utsMapOf({ class: "btn-text" }), "进入官网"),
        createElementVNode("text", utsMapOf({ class: "btn-subtext" }), "Enter Website")
      ], 8 /* PROPS */, ["onClick"])
    ])
  ])
}
const GenPagesIndexIndexStyles = [utsMapOf([["container", padStyleMapOf(utsMapOf([["width", "100%"], ["backgroundColor", "#f8f9fa"], ["display", "flex"], ["flexDirection", "column"]]))], ["status-bar", padStyleMapOf(utsMapOf([["width", "100%"], ["backgroundImage", "none"], ["backgroundColor", "rgba(0,0,0,0)"]]))], ["main-content", padStyleMapOf(utsMapOf([["flex", 1], ["display", "flex"], ["flexDirection", "column"], ["justifyContent", "center"], ["alignItems", "center"], ["textAlign", "center"], ["paddingTop", 20], ["paddingRight", 20], ["paddingBottom", 20], ["paddingLeft", 20]]))], ["logo-img", padStyleMapOf(utsMapOf([["width", 120], ["height", 120], ["marginBottom", 20]]))], ["title-container", padStyleMapOf(utsMapOf([["marginBottom", 10]]))], ["main-title", padStyleMapOf(utsMapOf([["fontSize", 48], ["fontWeight", "normal"], ["lineHeight", 1.2]]))], ["gold-text", padStyleMapOf(utsMapOf([["color", "#D4AF37"]]))], ["blue-text", padStyleMapOf(utsMapOf([["color", "#0c4da2"]]))], ["sub-title", padStyleMapOf(utsMapOf([["color", "#666666"], ["fontSize", 24], ["marginBottom", 30]]))], ["button-container", padStyleMapOf(utsMapOf([["paddingTop", 12], ["paddingRight", 30], ["paddingBottom", 12], ["paddingLeft", 30], ["backgroundColor", "#0c4da2"], ["color", "#ffffff"], ["borderTopLeftRadius", 5], ["borderTopRightRadius", 5], ["borderBottomRightRadius", 5], ["borderBottomLeftRadius", 5], ["cursor", "pointer"], ["transitionProperty", "backgroundColor"], ["transitionDuration", "0.3s"], ["backgroundColor:active", "#083778"]]))], ["btn-text", padStyleMapOf(utsMapOf([["fontSize", 18], ["color", "#ffffff"], ["marginBottom", 5]]))], ["btn-subtext", padStyleMapOf(utsMapOf([["fontSize", 14], ["color", "#ffffff"]]))], ["@TRANSITION", utsMapOf([["button-container", utsMapOf([["property", "backgroundColor"], ["duration", "0.3s"]])]])]])]
