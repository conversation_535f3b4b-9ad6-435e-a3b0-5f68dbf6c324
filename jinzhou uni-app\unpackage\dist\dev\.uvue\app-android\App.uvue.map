{"version": 3, "sources": ["App.uvue"], "names": [], "mappings": "AACC,IAAI,aAAY,GAAI,CAAA,CAAA;AACpB,MAAK,OAAQ,GAAE,SAAA,CAAA;IACd,QAAQ,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,YAAY,EAAA,gBAAA,CAAA,CAAA;IACzB,CAAC;IACD,MAAM,EAAE;QACP,OAAO,CAAC,GAAG,CAAC,UAAU,EAAA,gBAAA,CAAA,CAAA;IACvB,CAAC;IACD,MAAM,EAAE;QACP,OAAO,CAAC,GAAG,CAAC,UAAU,EAAA,iBAAA,CAAA,CAAA;IACvB,CAAC;IAED,mBAAmB,EAAE;QACpB,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAA,iBAAA,CAAA,CAAA;QACnC,IAAI,aAAY,IAAK,CAAC,EAAE;YACvB,GAAG,CAAC,SAAS,CAAC;gBACb,KAAK,EAAE,UAAU;gBACjB,QAAQ,EAAE,QAAQ;aAClB,CAAA,CAAA;YACD,aAAY,GAAI,IAAI,CAAC,GAAG,EAAC,CAAA;YACzB,UAAU,CAAC,GAAG,EAAC;gBACd,aAAY,GAAI,CAAA,CAAA;YACjB,CAAC,EAAE,IAAI,CAAA,CAAA;SACR;aAAO,IAAI,IAAI,CAAC,GAAG,EAAC,GAAI,aAAY,GAAI,IAAI,EAAE;YAC7C,aAAY,GAAI,IAAI,CAAC,GAAG,EAAC,CAAA;YACzB,GAAG,CAAC,IAAI,EAAC,CAAA;SACV;IACD,CAAC;IAED,MAAM,EAAE;QACP,OAAO,CAAC,GAAG,CAAC,UAAU,EAAA,iBAAA,CAAA,CAAA;IACvB,CAAC;CACF,CAAA,CAAA", "file": "App.uvue", "sourceRoot": "", "sourcesContent": ["<script lang=\"uts\">\r\n\tlet firstBackTime = 0\r\n\texport default {\r\n\t\tonLaunch: function () {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t},\r\n\t\tonShow: function () {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function () {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t},\r\n\r\n\t\tonLastPageBackPress: function () {\r\n\t\t\tconsole.log('App LastPageBackPress')\r\n\t\t\tif (firstBackTime == 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '再按一次退出应用',\r\n\t\t\t\t\tposition: 'bottom',\r\n\t\t\t\t})\r\n\t\t\t\tfirstBackTime = Date.now()\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tfirstBackTime = 0\r\n\t\t\t\t}, 2000)\r\n\t\t\t} else if (Date.now() - firstBackTime < 2000) {\r\n\t\t\t\tfirstBackTime = Date.now()\r\n\t\t\t\tuni.exit()\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonExit: function () {\r\n\t\t\tconsole.log('App Exit')\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n\t.uni-row {\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.uni-column {\r\n\t\tflex-direction: column;\r\n\t}\r\n</style>"]}