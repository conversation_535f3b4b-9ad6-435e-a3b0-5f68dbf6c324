{"version": 3, "sources": ["pages/webview/webview.uvue"], "names": [], "mappings": "AAOC,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI;QACH,OAAO;YACN,MAAM,EAAE,EAAC;SACV,CAAA;IACD,CAAC;IACD,MAAM,CAAC,OAAO;QACb,IAAI;YACH,aAAY;YACZ,IAAI,OAAM,IAAK,OAAO,CAAC,GAAG,EAAE;gBAC3B,IAAI,CAAC,MAAK,GAAI,UAAU,CAAC,iBAAiB,CAAC,kBAAC,CAAA,OAAA,CAAA,GAAA,CAAA,EAAA,mCAAA,CAAA,CAAA;aAC7C;iBAAO;gBACN,qBAAoB;gBACpB,IAAI,CAAC,MAAK,GAAI,oCAAmC,CAAA;aAClD;YAEA,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAA,mCAAA,CAAA,CAAA;SACjC;QAAE,OAAO,KAAK,KAAA,EAAE;YACf,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,EAAA,mCAAA,CAAA,CAAA;YAC9B,IAAI,CAAC,MAAK,GAAI,oCAAmC,CAAA;SAClD;IACD,CAAC;IACD,OAAO,EAAE;QACR,aAAa,CAAC,KAAK;YAClB,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,EAAA,mCAAA,CAAA,CAAA;QACzC,CAAC;QACD,WAAW,CAAC,KAAK;YAChB,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,MAAM,EAAA,mCAAA,CAAA,CAAA;YACrC,GAAG,CAAC,SAAS,CAAC;gBACb,KAAK,EAAE,QAAQ;gBACf,IAAI,EAAE,MAAK;aACX,CAAA,CAAA;QACF,CAAA;KACD;CACD,CAAA,CAAA;;;;;WAxCA,kBAAA,CAEO,MAAA,EAAA,QAAA,CAAA,EAFD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;QACtB,kBAAA,CAAiF,UAAA,EAAA,QAAA,CAAA;YAAtE,GAAG,EAAE,IAAA,CAAA,MAAM;YAAG,SAAO,EAAE,IAAA,CAAA,aAAa;YAAG,OAAK,EAAE,IAAA,CAAA,WAAW", "file": "pages/webview/webview.uvue", "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<web-view :src=\"webUrl\" @message=\"handleMessage\" @error=\"handleError\"></web-view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\twebUrl: ''\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\ttry {\n\t\t\t\t// 获取传入的URL参数\n\t\t\t\tif (options && options.url) {\n\t\t\t\t\tthis.webUrl = decodeURIComponent(options.url)\n\t\t\t\t} else {\n\t\t\t\t\t// 默认URL - 指向线上服务器的主页\n\t\t\t\t\tthis.webUrl = 'http://www.jinzhouil.com/main.html'\n\t\t\t\t}\n\n\t\t\t\tconsole.log('加载网页:', this.webUrl)\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('页面加载错误:', error)\n\t\t\t\tthis.webUrl = 'http://www.jinzhouil.com/main.html'\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\thandleMessage(event) {\n\t\t\t\tconsole.log('收到网页消息:', event.detail.data)\n\t\t\t},\n\t\t\thandleError(event) {\n\t\t\t\tconsole.error('网页加载错误:', event.detail)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网页加载失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\twidth: 100%;\n\t\theight: 100vh;\n\t}\n</style>\n"]}