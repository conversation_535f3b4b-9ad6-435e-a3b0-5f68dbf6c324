@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNI0CFFF19
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import io.dcloud.uniapp.extapi.showToast as uni_showToast
open class GenPagesWebviewWebview : BasePage {
    constructor(__ins: ComponentInternalInstance, __renderer: String?) : super(__ins, __renderer) {
        onLoad(fun(options: OnLoadOptions) {
            try {
                if (options && options.url) {
                    this.webUrl = decodeURIComponent(options.url)
                } else {
                    this.webUrl = "http://www.jinzhouil.com/main.html"
                }
                console.log("加载网页:", this.webUrl)
            }
             catch (error: Throwable) {
                console.error("页面加载错误:", error)
                this.webUrl = "http://www.jinzhouil.com/main.html"
            }
        }
        , __ins)
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        return createElementVNode("view", utsMapOf("class" to "container"), utsArrayOf(
            createElementVNode("web-view", utsMapOf("src" to _ctx.webUrl, "onMessage" to _ctx.handleMessage, "onError" to _ctx.handleError), null, 40, utsArrayOf(
                "src",
                "onMessage",
                "onError"
            ))
        ))
    }
    open var webUrl: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return utsMapOf("webUrl" to "")
    }
    open var handleMessage = ::gen_handleMessage_fn
    open fun gen_handleMessage_fn(event) {
        console.log("收到网页消息:", event.detail.data)
    }
    open var handleError = ::gen_handleError_fn
    open fun gen_handleError_fn(event) {
        console.error("网页加载错误:", event.detail)
        uni_showToast(ShowToastOptions(title = "网页加载失败", icon = "none"))
    }
    companion object {
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            normalizeCssStyles(utsArrayOf(
                styles0
            ), utsArrayOf(
                GenApp.styles
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return utsMapOf("container" to padStyleMapOf(utsMapOf("width" to "100%")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = utsMapOf()
        var emits: Map<String, Any?> = utsMapOf()
        var props = normalizePropsOptions(utsMapOf())
        var propsNeedCastKeys: UTSArray<String> = utsArrayOf()
        var components: Map<String, CreateVueComponent> = utsMapOf()
    }
}
