const __sfc__ = defineComponent({
    data() {
        return {
            webUrl: ''
        };
    },
    onLoad(options) {
        try {
            // 获取传入的URL参数
            if (options && options.url) {
                this.webUrl = decodeURIComponent(options.url);
            }
            else {
                // 默认URL - 指向线上服务器的主页
                this.webUrl = 'http://www.jinzhouil.com/main.html';
            }
            console.log('加载网页:', this.webUrl);
        }
        catch (error: any) {
            console.error('页面加载错误:', error);
            this.webUrl = 'http://www.jinzhouil.com/main.html';
        }
    },
    methods: {
        handleMessage(event) {
            console.log('收到网页消息:', event.detail.data);
        },
        handleError(event) {
            console.error('网页加载错误:', event.detail);
            uni.showToast({
                title: '网页加载失败',
                icon: 'none'
            });
        }
    }
});
export default __sfc__;
function GenPagesWebviewWebviewRender(this: InstanceType<typeof __sfc__>): any | null {
    const _ctx = this;
    const _cache = this.$.renderCache;
    return createElementVNode("view", utsMapOf({ class: "container" }), [
        createElementVNode("web-view", utsMapOf({
            src: _ctx.webUrl,
            onMessage: _ctx.handleMessage,
            onError: _ctx.handleError
        }), null, 40 /* PROPS, NEED_HYDRATION */, ["src", "onMessage", "onError"])
    ]);
}
const GenPagesWebviewWebviewStyles = [utsMapOf([["container", padStyleMapOf(utsMapOf([["width", "100%"]]))]])];
