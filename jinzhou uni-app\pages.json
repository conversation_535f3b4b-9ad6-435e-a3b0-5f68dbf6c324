{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "金舟国际物流",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/webview/webview",
			"style": {
				"navigationBarTitleText": "金舟国际物流",
				"navigationBarBackgroundColor": "#0c4da2",
				"navigationBarTextStyle": "white"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "white",
		"navigationBarTitleText": "金舟国际物流",
		"navigationBarBackgroundColor": "#0c4da2",
		"backgroundColor": "#f8f9fa"
	},
	"uniIdRouter": {},
	"condition" : { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [
			{
				"name": "", //模式名称
				"path": "", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到
			}
		]
	}
}
