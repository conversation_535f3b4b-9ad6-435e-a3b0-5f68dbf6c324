import 'D:/新建文件夹/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/index.ts';
import App from './App.uvue';
import { createSSRApp } from 'vue';
export function createApp(): UTSJSONObject {
    const app = createSSRApp(App);
    return {
        app
    };
}
export function main(app: IApp) {
    definePageRoutes();
    defineAppConfig();
    (createApp()['app'] as VueApp).mount(app, GenUniApp());
}
export class UniAppConfig extends io.dcloud.uniapp.appframe.AppConfig {
    override name: string = "金舟国际物流";
    override appid: string = "__UNI__0CFFF19";
    override versionName: string = "1.0.0";
    override versionCode: string = "100";
    override uniCompilerVersion: string = "4.66";
    constructor() { super(); }
}
import GenPagesIndexIndexClass from './pages/index/index.uvue?type=page';
import GenPagesWebviewWebviewClass from './pages/webview/webview.uvue?type=page';
function definePageRoutes() {
    __uniRoutes.push({ path: "pages/index/index", component: GenPagesIndexIndexClass, meta: { isQuit: true } as UniPageMeta, style: utsMapOf([["navigationBarTitleText", "金舟国际物流"], ["navigationStyle", "custom"]]) } as UniPageRoute);
    __uniRoutes.push({ path: "pages/webview/webview", component: GenPagesWebviewWebviewClass, meta: { isQuit: false } as UniPageMeta, style: utsMapOf([["navigationBarTitleText", "金舟国际物流"], ["navigationBarBackgroundColor", "#0c4da2"], ["navigationBarTextStyle", "white"]]) } as UniPageRoute);
}
const __uniTabBar: Map<string, any | null> | null = null;
const __uniLaunchPage: Map<string, any | null> = utsMapOf([["url", "pages/index/index"], ["style", utsMapOf([["navigationBarTitleText", "金舟国际物流"], ["navigationStyle", "custom"]])]]);
function defineAppConfig() {
    __uniConfig.entryPagePath = '/pages/index/index';
    __uniConfig.globalStyle = utsMapOf([["navigationBarTextStyle", "white"], ["navigationBarTitleText", "金舟国际物流"], ["navigationBarBackgroundColor", "#0c4da2"], ["backgroundColor", "#f8f9fa"]]);
    __uniConfig.getTabBarConfig = (): Map<string, any> | null => null;
    __uniConfig.tabBar = __uniConfig.getTabBarConfig();
    __uniConfig.conditionUrl = '';
    __uniConfig.uniIdRouter = utsMapOf();
    __uniConfig.ready = true;
}
//# sourceMappingURL=main.uts.map